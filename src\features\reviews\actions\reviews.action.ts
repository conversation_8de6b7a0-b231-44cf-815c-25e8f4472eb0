'use server';

import { TReview } from '../types/review.type';

export const getReviews = async (): Promise<{ data: TReview[] }> => {
  // TODO: Replace with actual API call
  const reviews: TReview[] = [
    {
      id: '1',
      rating: 5,
      review: 'This is a great property! I would highly recommend it.',
      user: {
        id: '1',
        name: '<PERSON>',
        image: 'https://randomuser.me/api/portraits/men/1.jpg',
      },
      property: {
        id: '1',
        name: 'Modern Apartment in Downtown',
      },
      createdAt: new Date().toISOString(),
    },
    {
      id: '2',
      rating: 4,
      review: 'A very comfortable and clean place. The host was very friendly.',
      user: {
        id: '2',
        name: '<PERSON>',
        image: 'https://randomuser.me/api/portraits/women/2.jpg',
      },
      property: {
        id: '2',
        name: 'Cozy Cottage in the Woods',
      },
      createdAt: new Date().toISOString(),
    },
  ];

  return { data: reviews };
};
