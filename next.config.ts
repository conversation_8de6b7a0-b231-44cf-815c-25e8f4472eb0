import type { NextConfig } from "next";

const nextConfig: NextConfig = {
    /* config options here */
    experimental: {
        useCache: true,
        staleTimes: {
            dynamic: 3 * 24 * 60 * 60, // 3 days
        },
        serverActions: {
            bodySizeLimit: "10mb",
        },
    },
    images: {
        remotePatterns: [
            {
                protocol: "https",
                hostname: "fmafiles-main.s3.me-central-1.amazonaws.com",
                pathname: "/images/**",
            }
        ]
    }
};

export default nextConfig;
