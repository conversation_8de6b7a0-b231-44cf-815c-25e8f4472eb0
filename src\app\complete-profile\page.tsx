import React from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { X } from 'lucide-react'
import Link from 'next/link'
import { PAGE_ROUTES } from '@/constants/page-routes'
import { 
    getIndustriesOptions, 
    getLocationsOptions, 
    getNationalitiesOptions 
} from '@/profile/actions/profile-roles.actions'
import { CompleteProfileFormOptionsProvider } from '@/profile/context/complete-profile-form-options-context'
import { FormStepsProvider } from '@/context/form-steps-context'
import IndividualProfileForm from '@/profile/components/verify-profile-form/individual/individual-profile-form'
import { IndividualRegistrationFormStoreProvider } from '@/context/individual-registration-form-store'
import ProfileFormProgressBar from '@/profile/components/verify-profile-form/progress-bar'
import Logo from '@/components/logo'
import AvatarDropdownMenu from '@/components/navbar/avatar-dropdown-menu'


async function CompleteProfilePage() {
    // Fetching select options 
    const [nationalities, locations, industries] = await Promise.all([
        getNationalitiesOptions(),
        getLocationsOptions(),
        getIndustriesOptions(),
    ])
    if (!nationalities.success) throw new Error(nationalities.message)
    if (!locations.success) throw new Error(locations.message)
    if (!industries.success) throw new Error(industries.message)
    const nationalitiesOptions = nationalities.data
    const locationsOptions = locations.data
    const industriesOptions = industries.data

    return (
        <FormStepsProvider totalSteps={6} initialStep={1}>
            <div className='bg-background min-h-svh relative pb-5'>
                <nav className='flex justify-between items-center px-dashboard-layout h-nav bg-background-light sticky top-0 z-20 shadow-sm'>
                    <Logo />
                    <AvatarDropdownMenu />
                </nav>
                <div className='grid w-11/12 max-w-3xl mx-auto relative rounded-lg overflow-hidden h-full mt-10'>
                    <ProfileFormProgressBar />

                    {/* Close Button */}
                    <Button
                        className='absolute top-2 right-2 text-muted-foreground/70 md:top-3 md:right-3'
                        variant={"ghost"}
                        size={"icon"}
                        asChild
                    >
                        <Link href={PAGE_ROUTES.dashboard}>
                            <X />
                        </Link>
                    </Button>

                    {/* Body */}
                    <div className='flex-1 px-6 py-10 bg-background-light '>
                        <div className='max-w-2xl mx-auto flex flex-col gap-10'>
                            <CompleteProfileFormOptionsProvider 
                                nationalities={nationalitiesOptions} 
                                locations={locationsOptions}
                                industries={industriesOptions}
                            >
                                <IndividualRegistrationFormStoreProvider>
                                    <IndividualProfileForm />
                                </IndividualRegistrationFormStoreProvider>
                            </CompleteProfileFormOptionsProvider>
                        </div>
                    </div>
                </div>
            </div>
        </FormStepsProvider>
    )
}

export default CompleteProfilePage